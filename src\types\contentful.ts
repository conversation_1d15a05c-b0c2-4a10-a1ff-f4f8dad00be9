// Contentful API response types
export interface ContentfulAsset {
  sys: {
    type: "Link";
    linkType: "Asset";
    id: string;
  };
}

export interface ContentfulSys {
  space: {
    sys: {
      type: "Link";
      linkType: "Space";
      id: string;
    };
  };
  id: string;
  type: "Entry";
  createdAt: string;
  updatedAt: string;
  environment: {
    sys: {
      id: string;
      type: "Link";
      linkType: "Environment";
    };
  };
  publishedVersion: number;
  revision: number;
  contentType: {
    sys: {
      type: "Link";
      linkType: "ContentType";
      id: string;
    };
  };
  locale: string;
}

export interface ContentfulTextNode {
  nodeType: "text";
  value: string;
  marks: any[];
  data: {};
}

export interface ContentfulParagraphNode {
  nodeType: "paragraph";
  data: {};
  content: ContentfulTextNode[];
}

export interface ContentfulDocument {
  nodeType: "document";
  data: {};
  content: ContentfulParagraphNode[];
}

export interface ContentfulBlogFields {
  title: string;
  description: string;
  slug: string;
  category: string;
  bannerImage: ContentfulAsset;
  thumbnail: ContentfulAsset;
  minuteRead: number;
  author: string;
  content: ContentfulDocument;
}

export interface ContentfulBlogEntry {
  metadata: {
    tags: any[];
    concepts: any[];
  };
  sys: ContentfulSys;
  fields: ContentfulBlogFields;
}

// Processed blog post interface for easier use in components
export interface BlogPost {
  id: string;
  title: string;
  description: string;
  slug: string;
  category: string;
  bannerImageUrl: string;
  thumbnailUrl: string;
  minuteRead: number;
  author: string;
  content: string[];
  createdAt: string;
  updatedAt: string;
}

// Base URL for Contentful assets
export const CONTENTFUL_ASSET_BASE_URL = "https://images.ctfassets.net/qpmxuq9j1cps";

// Helper function to convert Contentful asset ID to full URL
export const getContentfulImageUrl = (assetId: string): string => {
  return `${CONTENTFUL_ASSET_BASE_URL}/${assetId}`;
};

// Helper function to process Contentful blog entry to BlogPost
export const processContentfulBlogEntry = (entry: ContentfulBlogEntry): BlogPost => {
  const { fields, sys } = entry;
  
  // Extract text content from Contentful rich text
  const contentParagraphs = fields.content.content.map(paragraph => {
    return paragraph.content.map(textNode => textNode.value).join('');
  });

  return {
    id: sys.id,
    title: fields.title,
    description: fields.description,
    slug: fields.slug,
    category: fields.category,
    bannerImageUrl: getContentfulImageUrl(fields.bannerImage.sys.id),
    thumbnailUrl: getContentfulImageUrl(fields.thumbnail.sys.id),
    minuteRead: fields.minuteRead,
    author: fields.author,
    content: contentParagraphs,
    createdAt: sys.createdAt,
    updatedAt: sys.updatedAt,
  };
};
