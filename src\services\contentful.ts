import { ContentfulBlogEntry } from "@/types/contentful";

// Contentful API configuration
const CONTENTFUL_SPACE_ID = process.env.NEXT_PUBLIC_CONTENTFUL_SPACE_ID || "qpmxuq9j1cps";
const CONTENTFUL_ACCESS_TOKEN = process.env.NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN || "your-access-token";
const CONTENTFUL_API_BASE = "https://cdn.contentful.com";

/**
 * Fetch all blog entries from Contentful
 */
export async function fetchBlogEntries(): Promise<ContentfulBlogEntry[]> {
  try {
    const response = await fetch(
      `${CONTENTFUL_API_BASE}/spaces/${CONTENTFUL_SPACE_ID}/entries?content_type=euStudyBlog&access_token=${CONTENTFUL_ACCESS_TOKEN}`
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch blog entries: ${response.statusText}`);
    }

    const data = await response.json();
    return data.items as ContentfulBlogEntry[];
  } catch (error) {
    console.error("Error fetching blog entries:", error);
    return [];
  }
}

/**
 * Fetch a single blog entry by slug
 */
export async function fetchBlogEntryBySlug(slug: string): Promise<ContentfulBlogEntry | null> {
  try {
    const response = await fetch(
      `${CONTENTFUL_API_BASE}/spaces/${CONTENTFUL_SPACE_ID}/entries?content_type=euStudyBlog&fields.slug=${slug}&access_token=${CONTENTFUL_ACCESS_TOKEN}`
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch blog entry: ${response.statusText}`);
    }

    const data = await response.json();
    return data.items.length > 0 ? data.items[0] as ContentfulBlogEntry : null;
  } catch (error) {
    console.error("Error fetching blog entry by slug:", error);
    return null;
  }
}

/**
 * Sample data for development/testing
 */
export const sampleContentfulEntries: ContentfulBlogEntry[] = [
  {
    metadata: { tags: [], concepts: [] },
    sys: {
      space: { sys: { type: "Link", linkType: "Space", id: "qpmxuq9j1cps" } },
      id: "5eInDd9yy8iPWV6oDHniww",
      type: "Entry",
      createdAt: "2025-06-12T07:41:46.981Z",
      updatedAt: "2025-06-12T07:41:46.981Z",
      environment: { sys: { id: "master", type: "Link", linkType: "Environment" } },
      publishedVersion: 13,
      revision: 1,
      contentType: { sys: { type: "Link", linkType: "ContentType", id: "euStudyBlog" } },
      locale: "en-US"
    },
    fields: {
      title: "Exploring the Future of AI",
      description: "A deep dive into how AI is shaping our world.",
      slug: "exploring-the-future-of-ai",
      category: "Technology",
      bannerImage: { sys: { type: "Link", linkType: "Asset", id: "5dMdNIgp9NCjVZ4VEElpX5" } },
      thumbnail: { sys: { type: "Link", linkType: "Asset", id: "5ubI1koVBz7BQ1WVpeh7ZT" } },
      minuteRead: 5,
      author: "Jane Doe",
      content: {
        nodeType: "document",
        data: {},
        content: [
          {
            nodeType: "paragraph",
            data: {},
            content: [
              {
                nodeType: "text",
                value: "In recent years, artificial intelligence has revolutionized industries from healthcare to entertainment. This article explores the latest trends and future possibilities...",
                marks: [],
                data: {}
              }
            ]
          }
        ]
      }
    }
  },
  {
    metadata: { tags: [], concepts: [] },
    sys: {
      space: { sys: { type: "Link", linkType: "Space", id: "qpmxuq9j1cps" } },
      id: "2bKuFzAWq4cGYmK8sOeU6Y",
      type: "Entry",
      createdAt: "2025-06-10T09:30:22.123Z",
      updatedAt: "2025-06-10T09:30:22.123Z",
      environment: { sys: { id: "master", type: "Link", linkType: "Environment" } },
      publishedVersion: 8,
      revision: 1,
      contentType: { sys: { type: "Link", linkType: "ContentType", id: "euStudyBlog" } },
      locale: "en-US"
    },
    fields: {
      title: "Study Abroad: A Complete Guide",
      description: "Everything you need to know about studying abroad, from applications to cultural adaptation.",
      slug: "study-abroad-complete-guide",
      category: "Education",
      bannerImage: { sys: { type: "Link", linkType: "Asset", id: "3xMpQr8vN2jKL9wVeRt4Ys" } },
      thumbnail: { sys: { type: "Link", linkType: "Asset", id: "7nBvCx5mP1qW8zAeRt9Kj2" } },
      minuteRead: 8,
      author: "John Smith",
      content: {
        nodeType: "document",
        data: {},
        content: [
          {
            nodeType: "paragraph",
            data: {},
            content: [
              {
                nodeType: "text",
                value: "Studying abroad is one of the most transformative experiences a student can have. It opens doors to new cultures, languages, and perspectives that can shape your personal and professional future in profound ways.",
                marks: [],
                data: {}
              }
            ]
          }
        ]
      }
    }
  },
  {
    metadata: { tags: [], concepts: [] },
    sys: {
      space: { sys: { type: "Link", linkType: "Space", id: "qpmxuq9j1cps" } },
      id: "4vNxRt7mQ9pL2wKsOeU8Yz",
      type: "Entry",
      createdAt: "2025-06-08T14:15:33.456Z",
      updatedAt: "2025-06-08T14:15:33.456Z",
      environment: { sys: { id: "master", type: "Link", linkType: "Environment" } },
      publishedVersion: 5,
      revision: 1,
      contentType: { sys: { type: "Link", linkType: "ContentType", id: "euStudyBlog" } },
      locale: "en-US"
    },
    fields: {
      title: "Financial Planning for International Students",
      description: "Smart strategies for managing finances while studying abroad.",
      slug: "financial-planning-international-students",
      category: "Finance",
      bannerImage: { sys: { type: "Link", linkType: "Asset", id: "8qWxRt5nM3jKL7wVeRt2Ps" } },
      thumbnail: { sys: { type: "Link", linkType: "Asset", id: "6mBvCx9pQ1qW4zAeRt7Kj8" } },
      minuteRead: 6,
      author: "Sarah Johnson",
      content: {
        nodeType: "document",
        data: {},
        content: [
          {
            nodeType: "paragraph",
            data: {},
            content: [
              {
                nodeType: "text",
                value: "Managing finances as an international student requires careful planning and smart decision-making. From budgeting for tuition and living expenses to understanding currency exchange and banking options, financial literacy is crucial for academic success abroad.",
                marks: [],
                data: {}
              }
            ]
          }
        ]
      }
    }
  }
];

/**
 * Get sample data for development
 */
export function getSampleBlogEntries(): ContentfulBlogEntry[] {
  return sampleContentfulEntries;
}
