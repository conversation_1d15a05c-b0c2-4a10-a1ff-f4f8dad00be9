"use client";

import { useParams } from "next/navigation";
import { Box } from "@chakra-ui/react";
import PageLayout from "@/components/layout/PageLayout";
import BlogPostView from "@/components/blog/BlogPostView";
import { ContentfulBlogEntry, processContentfulBlogEntry } from "@/types/contentful";

// Sample blog data - in a real app, this would come from Contentful API
const sampleBlogData: ContentfulBlogEntry = {
  metadata: { tags: [], concepts: [] },
  sys: {
    space: { sys: { type: "Link", linkType: "Space", id: "qpmxuq9j1cps" } },
    id: "5eInDd9yy8iPWV6oDHniww",
    type: "Entry",
    createdAt: "2025-06-12T07:41:46.981Z",
    updatedAt: "2025-06-12T07:41:46.981Z",
    environment: { sys: { id: "master", type: "Link", linkType: "Environment" } },
    publishedVersion: 13,
    revision: 1,
    contentType: { sys: { type: "Link", linkType: "ContentType", id: "euStudyBlog" } },
    locale: "en-US"
  },
  fields: {
    title: "Exploring the Future of AI",
    description: "A deep dive into how AI is shaping our world and transforming industries.",
    slug: "exploring-the-future-of-ai",
    category: "Technology",
    bannerImage: { sys: { type: "Link", linkType: "Asset", id: "5dMdNIgp9NCjVZ4VEElpX5" } },
    thumbnail: { sys: { type: "Link", linkType: "Asset", id: "5ubI1koVBz7BQ1WVpeh7ZT" } },
    minuteRead: 5,
    author: "Jane Doe",
    content: {
      nodeType: "document",
      data: {},
      content: [
        {
          nodeType: "paragraph",
          data: {},
          content: [
            {
              nodeType: "text",
              value: "In recent years, artificial intelligence has revolutionized industries from healthcare to entertainment. This comprehensive exploration delves into the latest trends, breakthrough technologies, and future possibilities that AI presents for our rapidly evolving world.",
              marks: [],
              data: {}
            }
          ]
        },
        {
          nodeType: "paragraph",
          data: {},
          content: [
            {
              nodeType: "text",
              value: "Machine learning algorithms have become increasingly sophisticated, enabling computers to process vast amounts of data and make predictions with unprecedented accuracy. From autonomous vehicles navigating complex urban environments to medical diagnostic systems that can detect diseases earlier than human specialists, AI is reshaping how we approach problem-solving across multiple domains.",
              marks: [],
              data: {}
            }
          ]
        },
        {
          nodeType: "paragraph",
          data: {},
          content: [
            {
              nodeType: "text",
              value: "The integration of AI into everyday applications has also transformed user experiences. Natural language processing has made voice assistants more conversational and helpful, while computer vision technologies enable smartphones to recognize objects, translate text in real-time, and enhance photography through intelligent scene analysis.",
              marks: [],
              data: {}
            }
          ]
        },
        {
          nodeType: "paragraph",
          data: {},
          content: [
            {
              nodeType: "text",
              value: "Looking ahead, the future of AI promises even more exciting developments. Quantum computing could exponentially increase processing capabilities, while advances in neural network architectures may lead to more human-like reasoning abilities. As we continue to push the boundaries of what's possible, it's crucial to consider the ethical implications and ensure that AI development remains aligned with human values and societal benefits.",
              marks: [],
              data: {}
            }
          ]
        }
      ]
    }
  }
};

export default function BlogPostPage() {
  const params = useParams();
  const slug = params.slug as string;

  // In a real application, you would fetch the blog post data based on the slug
  // For now, we'll use the sample data
  const blogPost = processContentfulBlogEntry(sampleBlogData);

  return (
    <PageLayout>
      <Box pt="6rem">
        <BlogPostView blogPost={blogPost} />
      </Box>
    </PageLayout>
  );
}
