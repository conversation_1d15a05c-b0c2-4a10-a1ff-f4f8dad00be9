#!/usr/bin/env node

/**
 * Performance testing script
 * Tests the site performance and generates reports
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const SITE_URL = process.env.SITE_URL || 'http://localhost:3000';
const OUTPUT_DIR = './performance-reports';

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

console.log('🚀 Starting Performance Tests...\n');

// Test pages to analyze
const pages = [
  { name: 'Homepage', url: SITE_URL },
  { name: 'About', url: `${SITE_URL}/about` },
  { name: 'Courses', url: `${SITE_URL}/courses` },
  { name: 'Loans', url: `${SITE_URL}/loans` },
  { name: 'Blog', url: `${SITE_URL}/blog` },
];

async function runLighthouseTest(page) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const outputFile = path.join(OUTPUT_DIR, `${page.name.toLowerCase()}-${timestamp}.html`);
  
  console.log(`📊 Testing ${page.name} (${page.url})...`);
  
  try {
    const command = `lighthouse "${page.url}" \
      --output=html \
      --output-path="${outputFile}" \
      --chrome-flags="--headless --no-sandbox --disable-dev-shm-usage" \
      --only-categories=performance,accessibility,best-practices,seo \
      --throttling-method=simulate \
      --form-factor=desktop \
      --quiet`;
    
    execSync(command, { stdio: 'inherit' });
    
    console.log(`✅ ${page.name} report saved to: ${outputFile}\n`);
    return outputFile;
  } catch (error) {
    console.error(`❌ Error testing ${page.name}:`, error.message);
    return null;
  }
}

async function runPerformanceTests() {
  const results = [];
  
  for (const page of pages) {
    const reportFile = await runLighthouseTest(page);
    if (reportFile) {
      results.push({ page: page.name, report: reportFile });
    }
  }
  
  // Generate summary
  console.log('\n📋 Performance Test Summary:');
  console.log('================================');
  
  results.forEach(result => {
    console.log(`${result.page}: ${result.report}`);
  });
  
  console.log('\n🎯 Performance Optimization Tips:');
  console.log('- Check LCP (Largest Contentful Paint) < 2.5s');
  console.log('- Check FID (First Input Delay) < 100ms');
  console.log('- Check CLS (Cumulative Layout Shift) < 0.1');
  console.log('- Aim for Performance score > 90');
  console.log('- Aim for SEO score > 95');
  
  return results;
}

// Bundle size analysis
function analyzeBundleSize() {
  console.log('\n📦 Analyzing Bundle Size...');
  
  try {
    // Check if .next directory exists
    const nextDir = './.next';
    if (!fs.existsSync(nextDir)) {
      console.log('❌ .next directory not found. Run "npm run build" first.');
      return;
    }
    
    // Find JavaScript bundles
    const staticDir = path.join(nextDir, 'static');
    if (fs.existsSync(staticDir)) {
      const chunks = [];
      
      function findJSFiles(dir) {
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            findJSFiles(filePath);
          } else if (file.endsWith('.js')) {
            const size = stat.size;
            const sizeKB = (size / 1024).toFixed(2);
            chunks.push({ file, size: sizeKB });
          }
        });
      }
      
      findJSFiles(staticDir);
      
      // Sort by size
      chunks.sort((a, b) => parseFloat(b.size) - parseFloat(a.size));
      
      console.log('\n📊 Largest JavaScript Bundles:');
      chunks.slice(0, 10).forEach(chunk => {
        console.log(`${chunk.file}: ${chunk.size} KB`);
      });
      
      const totalSize = chunks.reduce((sum, chunk) => sum + parseFloat(chunk.size), 0);
      console.log(`\nTotal JS Bundle Size: ${totalSize.toFixed(2)} KB`);
      
      if (totalSize > 1000) {
        console.log('⚠️  Bundle size is large. Consider code splitting.');
      } else {
        console.log('✅ Bundle size looks good!');
      }
    }
  } catch (error) {
    console.error('❌ Error analyzing bundle size:', error.message);
  }
}

// Main execution
async function main() {
  try {
    await runPerformanceTests();
    analyzeBundleSize();
    
    console.log('\n🎉 Performance testing complete!');
    console.log(`📁 Reports saved in: ${OUTPUT_DIR}`);
    
  } catch (error) {
    console.error('❌ Performance testing failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { runPerformanceTests, analyzeBundleSize };
