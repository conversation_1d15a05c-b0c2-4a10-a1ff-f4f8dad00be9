{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/hooks/*": ["./src/hooks/*"], "@/lib/*": ["./src/lib/*"], "@/styles/*": ["./src/styles/*"], "@/types/*": ["./src/types/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/app/api/registerbootcamp"], "exclude": ["node_modules"]}