"use client";

import PageLayout from "@/components/layout/PageLayout";
import BlogHero from "./BlogHero";
import BlogPosts from "./BlogPosts";
import AnimatedElement from "@/components/common/AnimatedElement";
import PageTransition from "@/components/common/PageTransition";
import { getSampleBlogEntries } from "@/services/contentful";

export default function BlogPage() {
  // In a real application, you would fetch this data from Contentful API
  // For demonstration, we're using sample data
  const blogEntries = getSampleBlogEntries();

  return (
    <PageLayout>
      <PageTransition>
        {/* Hero Section */}
        <AnimatedElement animation="fadeIn" delay={0.2}>
          <BlogHero />
        </AnimatedElement>

        {/* Blog Posts Section */}
        <AnimatedElement animation="slideUp" delay={0.4}>
          <BlogPosts contentfulEntries={blogEntries} />
        </AnimatedElement>
      </PageTransition>
    </PageLayout>
  );
}
