# Blog System with Contentful Integration

This document explains the new blog system that integrates with Contentful CMS for dynamic content management.

## Overview

The blog system has been redesigned to work with Contentful API responses, providing a clean separation between content management and presentation. The system includes:

- **Type-safe Contentful integration** with TypeScript interfaces
- **Responsive blog post listing** with filtering and pagination
- **Individual blog post view** with rich content rendering
- **Automatic image URL generation** for Contentful assets
- **Sample data** for development and testing

## File Structure

```
src/
├── types/
│   └── contentful.ts              # TypeScript interfaces for Contentful data
├── components/
│   └── blog/
│       └── BlogPostView.tsx       # Individual blog post component
├── services/
│   └── contentful.ts              # Contentful API service functions
└── app/
    └── blog/
        ├── page.tsx               # Main blog listing page
        ├── BlogPosts.tsx          # Blog posts grid component
        └── [slug]/
            └── page.tsx           # Dynamic blog post page
```

## Key Components

### 1. BlogPosts Component (`src/app/blog/BlogPosts.tsx`)

The main component for displaying a grid of blog posts with filtering and pagination.

**Props:**
- `contentfulEntries?: ContentfulBlogEntry[]` - Array of Contentful blog entries

**Features:**
- Dynamic category filtering based on blog data
- Responsive pagination
- Thumbnail images with category badges
- Author information and read time
- Links to individual blog posts

### 2. BlogPostView Component (`src/components/blog/BlogPostView.tsx`)

Component for displaying individual blog posts with full content.

**Props:**
- `blogPost: BlogPost` - Processed blog post data

**Features:**
- Full-width banner image
- Author section with avatar
- Reading time indicator
- Small thumbnail image
- Responsive content layout
- SEO meta description

### 3. Contentful Types (`src/types/contentful.ts`)

TypeScript interfaces that match the Contentful API response structure:

- `ContentfulBlogEntry` - Raw Contentful API response
- `BlogPost` - Processed blog post for easier component use
- Helper functions for processing and image URL generation

## Usage Examples

### Basic Blog Listing

```tsx
import BlogPosts from "./BlogPosts";
import { getSampleBlogEntries } from "@/services/contentful";

export default function BlogPage() {
  const blogEntries = getSampleBlogEntries();
  
  return <BlogPosts contentfulEntries={blogEntries} />;
}
```

### Individual Blog Post

```tsx
import BlogPostView from "@/components/blog/BlogPostView";
import { processContentfulBlogEntry } from "@/types/contentful";

export default function BlogPostPage({ contentfulEntry }) {
  const blogPost = processContentfulBlogEntry(contentfulEntry);
  
  return <BlogPostView blogPost={blogPost} />;
}
```

### Fetching from Contentful API

```tsx
import { fetchBlogEntries } from "@/services/contentful";

export default async function BlogPage() {
  const contentfulEntries = await fetchBlogEntries();
  
  return <BlogPosts contentfulEntries={contentfulEntries} />;
}
```

## Contentful Setup

### Environment Variables

Add these to your `.env.local` file:

```env
NEXT_PUBLIC_CONTENTFUL_SPACE_ID=your_space_id
NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN=your_access_token
```

### Content Type Structure

The system expects a Contentful content type called `euStudyBlog` with these fields:

- `title` (Short text)
- `description` (Short text)
- `slug` (Short text)
- `category` (Short text)
- `bannerImage` (Media - single asset)
- `thumbnail` (Media - single asset)
- `minuteRead` (Number)
- `author` (Short text)
- `content` (Rich text)

## Image Handling

Images are automatically processed using the Contentful Images API:

- **Base URL**: `https://images.ctfassets.net/qpmxuq9j1cps/`
- **Asset IDs** are automatically converted to full URLs
- **Banner images** display full-width in blog post view
- **Thumbnails** are used in blog listing and as small squares in post view

## Sample Data

The system includes comprehensive sample data for development:

- 3 sample blog posts with different categories
- Realistic content with multiple paragraphs
- Proper Contentful API response structure
- Different authors and reading times

## Responsive Design

The blog system is fully responsive:

- **Mobile**: Single column layout, stacked elements
- **Tablet**: Two-column grid for blog listing
- **Desktop**: Optimized spacing and typography
- **Images**: Responsive sizing with proper aspect ratios

## SEO Features

- Meta descriptions as hidden elements
- Semantic HTML structure
- Proper heading hierarchy
- Alt text for all images
- Clean URL structure with slugs

## Future Enhancements

Potential improvements for the blog system:

1. **Rich Text Rendering**: Enhanced support for complex Contentful rich text
2. **Image Optimization**: Next.js Image component integration
3. **Search Functionality**: Full-text search across blog posts
4. **Related Posts**: Algorithm for suggesting related content
5. **Comments System**: Integration with comment platforms
6. **Social Sharing**: Share buttons for social media platforms

## Development Notes

- The system uses sample data by default for easy development
- All components are client-side rendered for interactivity
- TypeScript ensures type safety throughout the data flow
- Chakra UI provides consistent styling and responsive design
- The system is designed to be easily extensible for additional content types
