"use client";

import { Box, Container, Flex, Text, Avatar, VStack } from "@chakra-ui/react";

interface TestimonialCardProps {
  quote: string;
  name: string;
  initials: string;
  avatarBg?: string;
}

const TestimonialCard = ({ quote, name, initials, avatarBg = "#2F3540" }: TestimonialCardProps) => {
  return (
    <Box maxW="320px" w="100%">
      {/* Testimonial Quote Card */}
      <Box
        bg="white"
        borderRadius="16px"
        p="1.5rem"
        mb="1rem"
        boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
        position="relative"
      >
        <Text
          fontSize="0.9rem"
          lineHeight="1.5"
          color="#2F3540"
          fontWeight="400"
        >
          {quote}
        </Text>
      </Box>
      
      {/* Avatar and Name */}
      <Flex align="center" gap="0.75rem">
        <Avatar
          size="md"
          bg={avatarBg}
          color="white"
          name={initials}
          fontWeight="600"
          fontSize="1rem"
        />
        <Text
          color="white"
          fontWeight="500"
          fontSize="0.9rem"
        >
          {name}
        </Text>
      </Flex>
    </Box>
  );
};

export default function LoanTestimonials() {
  const testimonials = [
    {
      quote: "EU StudyAssist truly puts students first. From rent and research costs to flights, their financial support helps you focus on your studies, not money stress.",
      name: "Godwin Ogbiji",
      initials: "GA",
      avatarBg: "#2F3540"
    },
    {
      quote: "Such a great experience! The whole process was smooth and straightforward, and the team was incredibly responsive and supportive every step of the way. I felt guided and cared for throughout.",
      name: "Grace Alo",
      initials: "GA",
      avatarBg: "#2F3540"
    },
    {
      quote: "Thank you, EU StudyAssist. Your support made everything easier. You've been a key part of my success, and I'm truly grateful.",
      name: "Ayedun Favour",
      initials: "AF",
      avatarBg: "#2F3540"
    },
    {
      quote: "Overall, the services rendered by the EUstudyassist are invaluable and I would definitely recommend it to people.",
      name: "Azubuike Precious",
      initials: "PA",
      avatarBg: "#2F3540"
    }
  ];

  return (
    <Box
      bg="linear-gradient(135deg, #0E5FDC 0%, #1E6FE8 100%)"
      py={{ base: "4rem", md: "6rem" }}
      position="relative"
      overflow="hidden"
    >
      <Container maxW="1200px">
        <Flex
          direction={{ base: "column", md: "row" }}
          wrap="wrap"
          justify="center"
          align="flex-start"
          gap={{ base: "2.5rem", md: "2rem", lg: "3rem" }}
          px={{ base: "1rem", md: "2rem" }}
        >
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              quote={testimonial.quote}
              name={testimonial.name}
              initials={testimonial.initials}
              avatarBg={testimonial.avatarBg}
            />
          ))}
        </Flex>
      </Container>
    </Box>
  );
}
