"use client";

import { Box, Container, Flex, Text, Avatar, VStack } from "@chakra-ui/react";

interface TestimonialCardProps {
  quote: string;
  name: string;
  initials: string;
  avatarBg?: string;
}

const TestimonialCard = ({
  quote,
  name,
  initials,
  avatarBg = "#2F3540",
}: TestimonialCardProps) => {
  return (
    <VStack spacing="1rem" align="flex-start" maxW="320px" w="100%">
      {/* Blue Card with Avatar and Testimonial */}
      <Box
        bg="rgba(255, 255, 255, 0.15)"
        borderRadius="16px"
        p="1.5rem"
        backdropFilter="blur(10px)"
        border="1px solid rgba(255, 255, 255, 0.2)"
        w="100%"
      >
        <Flex align="flex-start" gap="1rem" mb="1rem">
          <Avatar
            size="md"
            bg={avatarBg}
            color="white"
            name={initials}
            fontWeight="600"
            fontSize="1rem"
            flexShrink={0}
          />
          <Text
            fontSize="0.9rem"
            lineHeight="1.5"
            color="white"
            fontWeight="400"
          >
            {quote}
          </Text>
        </Flex>
      </Box>

      {/* Name below the card */}
      <Text color="white" fontWeight="500" fontSize="0.9rem" ml="0.5rem">
        {name}
      </Text>
    </VStack>
  );
};

export default function LoanTestimonials() {
  const testimonials = [
    {
      quote:
        "EU StudyAssist truly puts students first. From rent and research costs to flights, their financial support helps you focus on your studies, not money stress.",
      name: "Godwin Ogbiji",
      initials: "GA",
      avatarBg: "#2F3540",
    },
    {
      quote:
        "Such a great experience! The whole process was smooth and straightforward, and the team was incredibly responsive and supportive every step of the way. I felt guided and cared for throughout.",
      name: "Grace Alo",
      initials: "GA",
      avatarBg: "#2F3540",
    },
    {
      quote:
        "Thank you, EU StudyAssist. Your support made everything easier. You've been a key part of my success, and I'm truly grateful.",
      name: "Ayedun Favour",
      initials: "AF",
      avatarBg: "#2F3540",
    },
    {
      quote:
        "Overall, the services rendered by the EUstudyassist are invaluable and I would definitely recommend it to people.",
      name: "Azubuike Precious",
      initials: "PA",
      avatarBg: "#2F3540",
    },
  ];

  return (
    <Box
      bg="linear-gradient(135deg, #0E5FDC 0%, #1E6FE8 100%)"
      py={{ base: "4rem", md: "6rem" }}
      position="relative"
      overflow="hidden"
    >
      <Container maxW="1200px">
        <Flex
          direction={{ base: "column", lg: "row" }}
          justify="center"
          align="flex-start"
          gap={{ base: "3rem", lg: "4rem" }}
          px={{ base: "1rem", md: "2rem" }}
        >
          {/* Left Column - First 2 testimonials */}
          <VStack spacing="3rem" align="flex-start" flex="1" maxW="500px">
            <TestimonialCard
              quote={testimonials[0].quote}
              name={testimonials[0].name}
              initials={testimonials[0].initials}
              avatarBg={testimonials[0].avatarBg}
            />
            <TestimonialCard
              quote={testimonials[2].quote}
              name={testimonials[2].name}
              initials={testimonials[2].initials}
              avatarBg={testimonials[2].avatarBg}
            />
          </VStack>

          {/* Right Column - Last 2 testimonials */}
          <VStack spacing="3rem" align="flex-start" flex="1" maxW="500px">
            <TestimonialCard
              quote={testimonials[1].quote}
              name={testimonials[1].name}
              initials={testimonials[1].initials}
              avatarBg={testimonials[1].avatarBg}
            />
            <TestimonialCard
              quote={testimonials[3].quote}
              name={testimonials[3].name}
              initials={testimonials[3].initials}
              avatarBg={testimonials[3].avatarBg}
            />
          </VStack>
        </Flex>
      </Container>
    </Box>
  );
}
