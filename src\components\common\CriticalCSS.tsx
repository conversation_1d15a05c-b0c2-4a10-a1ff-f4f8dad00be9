/**
 * Critical CSS component that inlines essential styles
 * to prevent layout shifts and improve performance
 */
export default function CriticalCSS() {
  return (
    <style jsx global>{`
      /* Critical styles to prevent layout shifts */
      
      /* Font loading optimization */
      @font-face {
        font-family: 'ClashDisplay';
        font-display: swap;
        font-weight: 400 700;
        src: url('/fonts/ClashDisplay-Variable.woff2') format('woff2');
      }
      
      @font-face {
        font-family: 'Inter';
        font-display: swap;
        font-weight: 400 700;
        src: url('/fonts/Inter-Variable.woff2') format('woff2');
      }
      
      /* Prevent layout shifts */
      img, video, iframe {
        max-width: 100%;
        height: auto;
      }
      
      /* Optimize rendering */
      * {
        box-sizing: border-box;
      }
      
      html {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
        scroll-behavior: smooth;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.5;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      /* Headings */
      h1, h2, h3, h4, h5, h6 {
        font-family: 'ClashDisplay', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 0;
        line-height: 1.2;
      }
      
      /* Hero section critical styles */
      #Heropage {
        min-height: 60vh;
        background-color: #0E5FDC;
        contain: layout style paint;
      }
      
      /* Image optimization */
      img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
      }
      
      /* Reduce motion for accessibility */
      @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
          scroll-behavior: auto !important;
        }
      }
      
      /* Loading states */
      .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }
      
      @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
      }
      
      /* Performance optimizations */
      .will-change-transform {
        will-change: transform;
      }
      
      .will-change-opacity {
        will-change: opacity;
      }
      
      /* Remove will-change after animation */
      .animation-complete {
        will-change: auto;
      }
    `}</style>
  );
}
