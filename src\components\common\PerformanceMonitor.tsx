"use client";

import { useEffect } from 'react';

/**
 * Performance monitoring component that tracks Core Web Vitals
 * and reports performance metrics for optimization
 */
export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run on client side and in production
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'production') {
      return;
    }

    // Track Core Web Vitals
    const trackWebVitals = async () => {
      try {
        const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals');

        // Largest Contentful Paint (LCP)
        getLCP((metric) => {
          console.log('LCP:', metric);
          // Send to analytics if needed
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'LCP',
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
        });

        // First Input Delay (FID)
        getFID((metric) => {
          console.log('FID:', metric);
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'FID',
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
        });

        // Cumulative Layout Shift (CLS)
        getCLS((metric) => {
          console.log('CLS:', metric);
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'CLS',
              value: Math.round(metric.value * 1000),
              non_interaction: true,
            });
          }
        });

        // First Contentful Paint (FCP)
        getFCP((metric) => {
          console.log('FCP:', metric);
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'FCP',
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
        });

        // Time to First Byte (TTFB)
        getTTFB((metric) => {
          console.log('TTFB:', metric);
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'TTFB',
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
        });

      } catch (error) {
        console.warn('Web Vitals tracking failed:', error);
      }
    };

    // Track custom performance metrics
    const trackCustomMetrics = () => {
      // Track page load time
      window.addEventListener('load', () => {
        const loadTime = performance.now();
        console.log('Page Load Time:', loadTime);
        
        if (window.gtag) {
          window.gtag('event', 'page_load_time', {
            event_category: 'Performance',
            value: Math.round(loadTime),
            non_interaction: true,
          });
        }
      });

      // Track navigation timing
      if ('navigation' in performance) {
        const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navTiming) {
          const metrics = {
            dns: navTiming.domainLookupEnd - navTiming.domainLookupStart,
            tcp: navTiming.connectEnd - navTiming.connectStart,
            request: navTiming.responseStart - navTiming.requestStart,
            response: navTiming.responseEnd - navTiming.responseStart,
            dom: navTiming.domContentLoadedEventEnd - navTiming.domContentLoadedEventStart,
          };

          console.log('Navigation Timing:', metrics);
        }
      }

      // Track resource loading
      if ('getEntriesByType' in performance) {
        const resources = performance.getEntriesByType('resource');
        const slowResources = resources.filter(resource => resource.duration > 1000);
        
        if (slowResources.length > 0) {
          console.warn('Slow loading resources:', slowResources);
        }
      }
    };

    // Initialize tracking
    trackWebVitals();
    trackCustomMetrics();

    // Track memory usage (if available)
    if ('memory' in performance) {
      const memoryInfo = (performance as any).memory;
      console.log('Memory Usage:', {
        used: Math.round(memoryInfo.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memoryInfo.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memoryInfo.jsHeapSizeLimit / 1048576) + ' MB',
      });
    }

  }, []);

  return null; // This component doesn't render anything
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}
