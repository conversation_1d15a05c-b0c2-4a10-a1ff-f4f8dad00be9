{"name": "student_assist", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html --chrome-flags=\"--headless\"", "lighthouse:ci": "lhci autorun", "perf:analyze": "npm run build:analyze && npm run lighthouse", "prepare": "husky install", "commit-no-hooks": "echo 'DO NOT use this'"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "eslintConfig": {"extends": ["prettier"]}, "engines": {"node": ">=20.0.0"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.1.5", "@chakra-ui/react": "^2.8.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@next/third-parties": "^14.1.4", "@paystack/inline-js": "2.12.6", "@vercel/analytics": "^1.1.1", "contentful": "^11.7.1", "formik": "^2.4.5", "framer-motion": "^10.18.0", "next": "^14.2.3", "nodemailer": "^6.9.8", "phosphor-react": "^1.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "stripe": "14.15.0", "swr": "^2.2.5", "web-vitals": "^5.0.3", "yup": "^1.4.0"}, "devDependencies": {"@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^14.1.4", "husky": "^9.1.7", "lint-staged": "^14.0.1", "typescript": "^5"}}