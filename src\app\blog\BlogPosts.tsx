"use client";

import { useState, useEffect } from "react";
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  Flex,
  Grid,
  GridItem,
  Image,
  Link,
  HStack,
  VStack,
  useBreakpointValue,
  Badge,
} from "@chakra-ui/react";
import { ChevronRightIcon, ChevronLeftIcon } from "@chakra-ui/icons";
import {
  BlogPost,
  ContentfulBlogEntry,
  processContentfulBlogEntry,
} from "@/types/contentful";
import AnimatedElement from "@/components/common/AnimatedElement";

interface BlogPostsProps {
  contentfulEntries?: ContentfulBlogEntry[];
}

// Sample Contentful data for demonstration
const sampleContentfulData: ContentfulBlogEntry[] = [
  {
    metadata: { tags: [], concepts: [] },
    sys: {
      space: { sys: { type: "Link", linkType: "Space", id: "qpmxuq9j1cps" } },
      id: "5eInDd9yy8iPWV6oDHniww",
      type: "Entry",
      createdAt: "2025-06-12T07:41:46.981Z",
      updatedAt: "2025-06-12T07:41:46.981Z",
      environment: {
        sys: { id: "master", type: "Link", linkType: "Environment" },
      },
      publishedVersion: 13,
      revision: 1,
      contentType: {
        sys: { type: "Link", linkType: "ContentType", id: "euStudyBlog" },
      },
      locale: "en-US",
    },
    fields: {
      title: "Exploring the Future of AI",
      description: "A deep dive into how AI is shaping our world.",
      slug: "exploring-the-future-of-ai",
      category: "Technology",
      bannerImage: {
        sys: { type: "Link", linkType: "Asset", id: "5dMdNIgp9NCjVZ4VEElpX5" },
      },
      thumbnail: {
        sys: { type: "Link", linkType: "Asset", id: "5ubI1koVBz7BQ1WVpeh7ZT" },
      },
      minuteRead: 5,
      author: "Jane Doe",
      content: {
        nodeType: "document",
        data: {},
        content: [
          {
            nodeType: "paragraph",
            data: {},
            content: [
              {
                nodeType: "text",
                value:
                  "In recent years, artificial intelligence has revolutionized industries from healthcare to entertainment. This article explores the latest trends and future possibilities...",
                marks: [],
                data: {},
              },
            ],
          },
        ],
      },
    },
  },
];

export default function BlogPosts({
  contentfulEntries = sampleContentfulData,
}: BlogPostsProps) {
  // Process Contentful entries to BlogPost format
  const blogData: BlogPost[] = contentfulEntries.map(
    processContentfulBlogEntry
  );

  // Extract unique categories from blog data
  const uniqueCategories = Array.from(
    new Set(blogData.map((post) => post.category))
  );
  const filterCategories = ["View All", ...uniqueCategories];

  // State for the active filter
  const [activeFilter, setActiveFilter] = useState("View All");
  // State for filtered blog posts
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>(blogData);
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);

  // Number of columns based on screen size
  const columns = useBreakpointValue({ base: 1, md: 2 });

  // Number of posts per page - responsive (3 on mobile, 6 on desktop)
  const postsPerPage = useBreakpointValue({ base: 3, md: 6 });

  // Responsive text for pagination buttons
  const prevButtonText = useBreakpointValue({ base: "", md: "Previous" });
  const nextButtonText = useBreakpointValue({ base: "", md: "Next" });

  // Get current posts
  const indexOfLastPost = currentPage * (postsPerPage || 6); // Fallback to 6 if undefined during initial render
  const indexOfFirstPost = indexOfLastPost - (postsPerPage || 6);
  const currentPosts = filteredPosts.slice(indexOfFirstPost, indexOfLastPost);

  // Filter posts when activeFilter changes
  useEffect(() => {
    if (activeFilter === "View All") {
      setFilteredPosts(blogData);
    } else {
      const filtered = blogData.filter(
        (post) => post.category === activeFilter
      );
      setFilteredPosts(filtered);
    }
    // Reset to first page when filter changes
    setCurrentPage(1);
  }, [activeFilter, blogData]);

  // Reset to first page when postsPerPage changes (screen size changes)
  useEffect(() => {
    // Check if we're beyond the last page after screen size change
    if (
      postsPerPage &&
      currentPage > Math.ceil(filteredPosts.length / postsPerPage)
    ) {
      setCurrentPage(1);
    }
  }, [postsPerPage, filteredPosts.length, currentPage]);

  return (
    <Box py={{ base: 10, md: 16 }} bg="white">
      <Container maxW="100%" px="0">
        {/* Section Heading */}
        <Heading
          as="h2"
          fontSize={{ base: "3xl", md: "4xl", lg: "5xl" }}
          fontWeight={700}
          color="#000"
          textAlign="center"
          mb={10}
        >
          Latest Blog Posts
        </Heading>

        {/* Filter Buttons */}
        <Box
          position="relative"
          mb={{ base: 10, md: 14 }}
          maxW={{ base: "80%", md: "90%" }}
          mx="auto"
          // ml={{base: "1rem"}}
        >
          {/* Horizontal Line */}
          <Box
            position="absolute"
            bottom="0"
            left="0"
            right="0"
            height="2px"
            bg="gray.300"
            zIndex={0}
          />

          <Flex
            overflowX="auto"
            pb={2}
            className="filter-tabs"
            position="relative"
          >
            {filterCategories.map((category) => (
              <Box
                key={category}
                as="button"
                fontSize={{ base: "sm", md: "md" }}
                fontWeight={600}
                color={activeFilter === category ? "black" : "gray.600"}
                position="relative"
                pr={{ base: 3, md: 5 }}
                // py={3}
                mr={{ base: 2, md: 6 }}
                bg="transparent"
                _hover={{
                  color: "black",
                }}
                onClick={() => setActiveFilter(category)}
                transition="all 0.3s"
                whiteSpace="nowrap"
              >
                {category}
                {/* Active indicator with glow effect */}
                {activeFilter === category && (
                  <Box
                    position="absolute"
                    bottom="-8px"
                    left="0"
                    right="0"
                    height="3px"
                    bg="black"
                    borderRadius="full"
                    boxShadow="0 0 8px 1px rgba(14, 95, 220, 0.5)"
                    zIndex={1}
                  />
                )}
              </Box>
            ))}
          </Flex>
        </Box>

        {/* Blog Posts Grid */}
        <Grid
          templateColumns={{
            base: "1fr",
            md: "repeat(2, 1fr)",
          }}
          gap={{ base: 6, md: 6 }}
          px={{ base: 4, md: 0 }}
          maxW={{ md: "90%" }}
          mx="auto"
        >
          {currentPosts.map((post, index) => (
            <GridItem key={post.id}>
              <Box
                borderRadius="xl"
                overflow="hidden"
                bg="white"
                boxShadow="0px 4px 10px rgba(0, 0, 0, 0.05)"
                transition="transform 0.2s, box-shadow 0.2s"
                maxW="95%"
                mx="auto"
                _hover={{
                  boxShadow: "0px 6px 15px rgba(0, 0, 0, 0.1)",
                }}
              >
                {/* Blog Post Image */}
                <Box position="relative">
                  <Image
                    src={post.thumbnailUrl}
                    alt={post.title}
                    w="100%"
                    h="280px"
                    objectFit="cover"
                    objectPosition="center top"
                    borderRadius="xl"
                  />

                  {/* Category Badge */}
                  <Badge
                    position="absolute"
                    top="16px"
                    left="16px"
                    bg="#0E5FDC"
                    color="white"
                    fontSize="xs"
                    fontWeight="600"
                    px="3"
                    py="1"
                    borderRadius="md"
                    textTransform="uppercase"
                  >
                    {post.category}
                  </Badge>

                  {/* Featured badge for first post */}
                  {index === 0 && (
                    <Badge
                      position="absolute"
                      top="16px"
                      right="16px"
                      bg="#F69127"
                      color="white"
                      fontSize="xs"
                      fontWeight="600"
                      px="3"
                      py="1"
                      borderRadius="md"
                    >
                      Featured
                    </Badge>
                  )}
                </Box>

                {/* Blog Post Content */}
                <Box pt={6} pb={4} px={4}>
                  <Flex justify="space-between" align="center" mb={3}>
                    <Heading
                      as="h3"
                      fontSize={{ base: "xl", md: "2xl" }}
                      fontWeight={700}
                      color="#000"
                      noOfLines={1}
                      flex="1"
                    >
                      {post.title}
                    </Heading>
                    <Text fontSize="xs" color="gray.500" ml={2}>
                      {post.minuteRead} min read
                    </Text>
                  </Flex>

                  <Text
                    color="gray.700"
                    fontSize={{ base: "sm", md: "md" }}
                    mb={4}
                    noOfLines={3}
                    lineHeight="1.6"
                  >
                    {post.description}
                  </Text>

                  <Flex justify="space-between" align="center">
                    <Text fontSize="xs" color="gray.500">
                      By {post.author}
                    </Text>
                    <Link
                      href={`/blog/${post.slug}`}
                      display="inline-flex"
                      alignItems="center"
                      color="#0E5FDC"
                      fontWeight={600}
                      fontSize={{ base: "sm", md: "md" }}
                      _hover={{ textDecoration: "none", color: "#0B4DB0" }}
                    >
                      Read Post <ChevronRightIcon boxSize={5} ml={1} />
                    </Link>
                  </Flex>
                </Box>
              </Box>
            </GridItem>
          ))}
        </Grid>

        {/* Pagination Controls */}
        {filteredPosts.length > (postsPerPage || 6) && (
          <Flex
            justify="center"
            mt={14}
            mb={20}
            gap={{ base: 1, md: 3 }}
            bg="gray.50"
            py={{ base: 3, md: 5 }}
            px={{ base: 2, md: 4 }}
            borderRadius="lg"
            boxShadow="sm"
            maxW="fit-content"
            mx="auto"
            flexWrap="wrap"
          >
            <Button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              isDisabled={currentPage === 1}
              variant="outline"
              colorScheme="blue"
              borderColor="#0E5FDC"
              color="#0E5FDC"
              _hover={{ bg: "blue.50" }}
              size="md"
              px={{ base: 2, md: 4 }}
              borderRadius="md"
              leftIcon={<ChevronLeftIcon />}
              fontSize={{ base: "sm", md: "md" }}
            >
              {prevButtonText}
            </Button>

            {/* Page Numbers - Show max 5 page buttons */}
            <Flex
              align="center"
              gap={{ base: 1, md: 2 }}
              flexWrap="wrap"
              justify="center"
            >
              {(() => {
                const totalPages = Math.ceil(
                  filteredPosts.length / (postsPerPage || 6)
                );
                // Logic to show limited page numbers with ellipsis
                let pagesToShow = [];

                if (totalPages <= 5) {
                  // If 5 or fewer pages, show all
                  pagesToShow = Array.from(
                    { length: totalPages },
                    (_, i) => i + 1
                  );
                } else {
                  // Always include first and last page
                  if (currentPage <= 3) {
                    // Near the start
                    pagesToShow = [1, 2, 3, 4, "...", totalPages];
                  } else if (currentPage >= totalPages - 2) {
                    // Near the end
                    pagesToShow = [
                      1,
                      "...",
                      totalPages - 3,
                      totalPages - 2,
                      totalPages - 1,
                      totalPages,
                    ];
                  } else {
                    // Somewhere in the middle
                    pagesToShow = [
                      1,
                      "...",
                      currentPage - 1,
                      currentPage,
                      currentPage + 1,
                      "...",
                      totalPages,
                    ];
                  }
                }

                return pagesToShow.map((page, index) => {
                  if (page === "...") {
                    return (
                      <Text
                        key={`ellipsis-${index}`}
                        color="gray.500"
                        fontWeight="bold"
                        px={{ base: 1, md: 2 }}
                        fontSize={{ base: "sm", md: "md" }}
                      >
                        ...
                      </Text>
                    );
                  }

                  return (
                    <Button
                      key={`page-${page}`}
                      onClick={() => setCurrentPage(Number(page))}
                      variant={currentPage === page ? "solid" : "outline"}
                      bg={currentPage === page ? "#0E5FDC" : "transparent"}
                      color={currentPage === page ? "white" : "#0E5FDC"}
                      borderColor="#0E5FDC"
                      _hover={{
                        bg: currentPage === page ? "#0B4DB0" : "blue.50",
                      }}
                      size={{ base: "sm", md: "md" }}
                      borderRadius="md"
                      minW={{ base: "30px", md: "40px" }}
                      p={{ base: "0", md: "2" }}
                      fontSize={{ base: "sm", md: "md" }}
                    >
                      {page}
                    </Button>
                  );
                });
              })()}
            </Flex>

            <Button
              onClick={() =>
                setCurrentPage((prev) =>
                  Math.min(
                    prev + 1,
                    Math.ceil(filteredPosts.length / (postsPerPage || 6))
                  )
                )
              }
              isDisabled={
                currentPage ===
                Math.ceil(filteredPosts.length / (postsPerPage || 6))
              }
              variant="outline"
              colorScheme="blue"
              borderColor="#0E5FDC"
              color="#0E5FDC"
              _hover={{ bg: "blue.50" }}
              size="md"
              px={{ base: 2, md: 4 }}
              borderRadius="md"
              rightIcon={<ChevronRightIcon />}
              fontSize={{ base: "sm", md: "md" }}
            >
              {nextButtonText}
            </Button>
          </Flex>
        )}
      </Container>
    </Box>
  );
}
