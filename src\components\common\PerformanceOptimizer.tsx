"use client";

import { useEffect } from 'react';

/**
 * Performance optimization component that applies various optimizations
 * to improve Lighthouse scores and overall site performance
 */
export default function PerformanceOptimizer() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // 1. Preload critical resources
    const preloadCriticalResources = () => {
      // Preload critical fonts
      const fontPreloads = [
        { href: '/fonts/ClashDisplay-Variable.woff2', as: 'font', type: 'font/woff2' },
        { href: '/fonts/Inter-Variable.woff2', as: 'font', type: 'font/woff2' },
      ];

      fontPreloads.forEach(({ href, as, type }) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = href;
        link.as = as;
        link.type = type;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });

      // Preconnect to external domains
      const preconnectDomains = [
        'https://cdn.contentful.com',
        'https://images.ctfassets.net',
        'https://www.googletagmanager.com',
        'https://www.google-analytics.com',
      ];

      preconnectDomains.forEach(domain => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = domain;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });
    };

    // 2. Optimize images loading
    const optimizeImages = () => {
      // Add loading="lazy" to all images that don't have it
      const images = document.querySelectorAll('img:not([loading])');
      images.forEach(img => {
        if (!img.hasAttribute('loading')) {
          img.setAttribute('loading', 'lazy');
        }
      });

      // Add decoding="async" for better performance
      const allImages = document.querySelectorAll('img');
      allImages.forEach(img => {
        if (!img.hasAttribute('decoding')) {
          img.setAttribute('decoding', 'async');
        }
      });
    };

    // 3. Optimize third-party scripts
    const optimizeThirdPartyScripts = () => {
      // Defer non-critical scripts
      const scripts = document.querySelectorAll('script[src]:not([defer]):not([async])');
      scripts.forEach(script => {
        if (!script.src.includes('gtag') && !script.src.includes('analytics')) {
          script.setAttribute('defer', '');
        }
      });
    };

    // 4. Reduce layout shifts
    const reduceLayoutShifts = () => {
      // Add CSS to prevent layout shifts
      const style = document.createElement('style');
      style.textContent = `
        /* Prevent layout shifts */
        img, video, iframe {
          max-width: 100%;
          height: auto;
        }
        
        /* Optimize font loading */
        @font-face {
          font-family: 'ClashDisplay';
          font-display: swap;
        }
        
        @font-face {
          font-family: 'Inter';
          font-display: swap;
        }
        
        /* Optimize animations */
        * {
          will-change: auto;
        }
        
        /* Reduce motion for users who prefer it */
        @media (prefers-reduced-motion: reduce) {
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `;
      document.head.appendChild(style);
    };

    // 5. Optimize scroll performance
    const optimizeScrolling = () => {
      // Use passive event listeners for better scroll performance
      let ticking = false;
      
      const updateScrollPosition = () => {
        ticking = false;
        // Any scroll-based updates can go here
      };

      const requestTick = () => {
        if (!ticking) {
          requestAnimationFrame(updateScrollPosition);
          ticking = true;
        }
      };

      window.addEventListener('scroll', requestTick, { passive: true });
      
      return () => {
        window.removeEventListener('scroll', requestTick);
      };
    };

    // 6. Optimize resource hints
    const addResourceHints = () => {
      // Add DNS prefetch for external domains
      const dnsPrefetchDomains = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://www.googletagmanager.com',
      ];

      dnsPrefetchDomains.forEach(domain => {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = domain;
        document.head.appendChild(link);
      });
    };

    // 7. Optimize critical rendering path
    const optimizeCriticalPath = () => {
      // Remove unused CSS (this would typically be done at build time)
      // For now, we'll just ensure critical CSS is inlined
      
      // Optimize web fonts loading
      if ('fonts' in document) {
        // Preload critical fonts
        const fontPromises = [
          new FontFace('ClashDisplay', 'url(/fonts/ClashDisplay-Variable.woff2)').load(),
          new FontFace('Inter', 'url(/fonts/Inter-Variable.woff2)').load(),
        ];

        Promise.all(fontPromises).then(fonts => {
          fonts.forEach(font => {
            document.fonts.add(font);
          });
        }).catch(error => {
          console.warn('Font loading failed:', error);
        });
      }
    };

    // Apply all optimizations
    const applyOptimizations = () => {
      try {
        preloadCriticalResources();
        optimizeImages();
        optimizeThirdPartyScripts();
        reduceLayoutShifts();
        addResourceHints();
        optimizeCriticalPath();
        
        // Return cleanup function for scroll optimization
        return optimizeScrolling();
      } catch (error) {
        console.warn('Performance optimization failed:', error);
      }
    };

    // Use requestIdleCallback for non-critical optimizations
    if ('requestIdleCallback' in window) {
      const cleanup = window.requestIdleCallback(applyOptimizations);
      return () => {
        if ('cancelIdleCallback' in window) {
          window.cancelIdleCallback(cleanup);
        }
      };
    } else {
      // Fallback for browsers without requestIdleCallback
      const timeoutId = setTimeout(applyOptimizations, 100);
      return () => clearTimeout(timeoutId);
    }
  }, []);

  return null; // This component doesn't render anything
}
