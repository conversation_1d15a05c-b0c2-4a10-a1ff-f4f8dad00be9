* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth !important;
  box-sizing: border-box;
}

body {
  scroll-behavior: smooth !important;
  box-sizing: border-box;
  font-family: "Inter", sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "ClashDisplay", sans-serif;
}

.btn:hover {
  background-color: rgba(52, 97, 255, 1);
}

.price-container {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  position: relative; /* Needed for absolute positioning of the pseudo-element */
}

.price-container::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  border-top: 2px solid white;
  transform-origin: left center;
  transform: translateY(-50%);
}
