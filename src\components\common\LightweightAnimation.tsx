"use client";

import { Box, BoxProps } from "@chakra-ui/react";
import { useEffect, useState, useRef } from "react";

interface LightweightAnimationProps extends BoxProps {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
  animationType?: "fadeIn" | "slideUp" | "slideLeft" | "slideRight";
}

/**
 * Lightweight animation component using CSS transitions
 * Much smaller bundle size than Framer Motion
 */
export default function LightweightAnimation({
  children,
  delay = 0,
  duration = 0.6,
  animationType = "fadeIn",
  ...boxProps
}: LightweightAnimationProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !hasAnimated) {
          setTimeout(() => {
            setIsVisible(true);
            setHasAnimated(true);
          }, delay * 1000);
        }
      },
      {
        threshold: 0.1,
        rootMargin: "50px",
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [delay, hasAnimated]);

  const getInitialStyles = () => {
    switch (animationType) {
      case "fadeIn":
        return {
          opacity: 0,
          transform: "none",
        };
      case "slideUp":
        return {
          opacity: 0,
          transform: "translateY(30px)",
        };
      case "slideLeft":
        return {
          opacity: 0,
          transform: "translateX(-30px)",
        };
      case "slideRight":
        return {
          opacity: 0,
          transform: "translateX(30px)",
        };
      default:
        return {
          opacity: 0,
          transform: "none",
        };
    }
  };

  const getVisibleStyles = () => {
    return {
      opacity: 1,
      transform: "translate(0, 0)",
    };
  };

  return (
    <Box
      ref={elementRef}
      style={{
        ...(isVisible ? getVisibleStyles() : getInitialStyles()),
        transition: `all ${duration}s ease-out`,
        willChange: "transform, opacity",
      }}
      {...boxProps}
    >
      {children}
    </Box>
  );
}
