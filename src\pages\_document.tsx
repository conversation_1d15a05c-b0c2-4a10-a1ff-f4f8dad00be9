import { Html, <PERSON>, Main, NextScript } from "next/document";

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Preload critical fonts */}
        <link
          rel="preload"
          href="/fonts/Inter_24pt-Regular.ttf"
          as="font"
          type="font/truetype"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/ClashDisplay-Regular.ttf"
          as="font"
          type="font/truetype"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/ClashDisplay-Medium.ttf"
          as="font"
          type="font/truetype"
          crossOrigin="anonymous"
        />

        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />

        {/* Add font display swap for better performance */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            @font-face {
              font-family: 'Inter';
              font-style: normal;
              font-weight: 400;
              font-display: swap;
              src: url('/fonts/Inter_24pt-Regular.ttf') format('truetype');
            }
            @font-face {
              font-family: 'ClashDisplay';
              font-style: normal;
              font-weight: 400;
              font-display: swap;
              src: url('/fonts/ClashDisplay-Regular.ttf') format('truetype');
            }
            @font-face {
              font-family: 'ClashDisplay';
              font-style: normal;
              font-weight: 500;
              font-display: swap;
              src: url('/fonts/ClashDisplay-Medium.ttf') format('truetype');
            }
          `,
          }}
        />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
