"use client";

import Image from "next/image";
import { Box, BoxProps } from "@chakra-ui/react";

interface CriticalImageProps extends Omit<BoxProps, "children"> {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  objectFit?: "cover" | "contain" | "fill" | "none" | "scale-down";
}

/**
 * Critical image component optimized for performance
 * Uses Next.js Image with explicit dimensions to prevent layout shifts
 */
export default function CriticalImage({
  src,
  alt,
  width,
  height,
  priority = false,
  quality = 85,
  sizes = "100vw",
  objectFit = "cover",
  ...boxProps
}: CriticalImageProps) {
  return (
    <Box
      position="relative"
      width={`${width}px`}
      height={`${height}px`}
      overflow="hidden"
      {...boxProps}
    >
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        quality={quality}
        sizes={sizes}
        style={{
          objectFit,
          width: "100%",
          height: "100%",
        }}
      />
    </Box>
  );
}
