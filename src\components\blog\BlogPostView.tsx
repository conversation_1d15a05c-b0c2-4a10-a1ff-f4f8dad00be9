"use client";

import React from "react";
import {
  Box,
  Container,
  Heading,
  Text,
  Image,
  Badge,
  Flex,
  Avatar,
  VStack,
  HStack,
} from "@chakra-ui/react";
import { BlogPost } from "@/types/contentful";

interface BlogPostViewProps {
  blogPost: BlogPost;
}

const BlogPostView: React.FC<BlogPostViewProps> = ({ blogPost }) => {
  return (
    <Box bg="white" minH="100vh">
      {/* Meta description as hidden element for SEO */}
      <Box as="meta" name="description" content={blogPost.description} display="none" />
      
      {/* Banner Image - Full Width */}
      <Box position="relative" w="100%" h={{ base: "250px", md: "400px", lg: "500px" }}>
        <Image
          src={blogPost.bannerImageUrl}
          alt={blogPost.title}
          w="100%"
          h="100%"
          objectFit="cover"
          objectPosition="center"
        />
        
        {/* Category Badge Overlay */}
        <Badge
          position="absolute"
          top="20px"
          left="20px"
          bg="#0E5FDC"
          color="white"
          fontSize="sm"
          fontWeight="600"
          px="3"
          py="2"
          borderRadius="md"
          textTransform="uppercase"
        >
          {blogPost.category}
        </Badge>
      </Box>

      <Container maxW="4xl" py={{ base: 8, md: 12 }}>
        {/* Article Header */}
        <VStack spacing={6} align="stretch" mb={8}>
          {/* Title */}
          <Heading
            as="h1"
            fontSize={{ base: "2xl", md: "3xl", lg: "4xl" }}
            fontWeight="700"
            color="#000"
            lineHeight="1.2"
            fontFamily="ClashDisplay"
          >
            {blogPost.title}
          </Heading>

          {/* Author and Meta Information */}
          <Flex
            direction={{ base: "column", md: "row" }}
            justify="space-between"
            align={{ base: "flex-start", md: "center" }}
            gap={4}
          >
            {/* Author Section */}
            <HStack spacing={3}>
              <Avatar
                size="md"
                name={blogPost.author}
                bg="#2F3540"
                color="white"
                fontWeight="600"
              />
              <VStack spacing={0} align="flex-start">
                <Text fontWeight="600" fontSize="md" color="#000">
                  {blogPost.author}
                </Text>
                <Text fontSize="sm" color="gray.600">
                  {new Date(blogPost.createdAt).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </Text>
              </VStack>
            </HStack>

            {/* Reading Time and Thumbnail */}
            <HStack spacing={4}>
              <Text fontSize="sm" color="gray.600" fontWeight="500">
                {blogPost.minuteRead} min read
              </Text>
              
              {/* Thumbnail - Small Square */}
              <Image
                src={blogPost.thumbnailUrl}
                alt={`${blogPost.title} thumbnail`}
                w="60px"
                h="60px"
                objectFit="cover"
                borderRadius="md"
                border="2px solid"
                borderColor="gray.200"
              />
            </HStack>
          </Flex>
        </VStack>

        {/* Article Content */}
        <Box
          fontSize={{ base: "md", md: "lg" }}
          lineHeight="1.8"
          color="#2F3540"
          maxW="none"
        >
          {blogPost.content.map((paragraph, index) => (
            <Text
              key={index}
              mb={6}
              fontSize={{ base: "md", md: "lg" }}
              lineHeight="1.8"
              color="#2F3540"
            >
              {paragraph}
            </Text>
          ))}
        </Box>

        {/* Article Footer */}
        <Box
          mt={12}
          pt={8}
          borderTop="1px solid"
          borderColor="gray.200"
        >
          <Flex
            direction={{ base: "column", md: "row" }}
            justify="space-between"
            align={{ base: "flex-start", md: "center" }}
            gap={4}
          >
            <VStack spacing={2} align="flex-start">
              <Text fontSize="sm" color="gray.600">
                Published on {new Date(blogPost.createdAt).toLocaleDateString()}
              </Text>
              {blogPost.updatedAt !== blogPost.createdAt && (
                <Text fontSize="sm" color="gray.500">
                  Last updated on {new Date(blogPost.updatedAt).toLocaleDateString()}
                </Text>
              )}
            </VStack>
            
            <Badge
              bg="gray.100"
              color="gray.700"
              fontSize="xs"
              fontWeight="500"
              px="3"
              py="1"
              borderRadius="full"
            >
              {blogPost.category}
            </Badge>
          </Flex>
        </Box>
      </Container>
    </Box>
  );
};

export default BlogPostView;
